"use client";

import { useState, useRef, useCallback, useEffect } from "react";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Upload,
  Sparkles,
  Download,
  Plus,
  X,
  Camera,
  Heart,
  Star,
  Crown,
  Flower,
  Mountain,
  Church,
  Waves,
  TreePine,
  Castle,
  Wine,
  Building,
  AlertCircle,
  Lock,
  Zap,
  ChevronDown,
  Check,
  Eye,
  Settings
} from "lucide-react";
import Image from "next/image";
import { useSession } from "next-auth/react";
import { signIn } from "next-auth/react";
// 订阅系统组件导入 - Subscription system component imports
import { CreditDisplay } from "@/components/subscription/CreditDisplay";
import { SubscriptionModal } from "@/components/subscription/SubscriptionModal";
import { hasEnoughCredits, consumeCredits } from "@/lib/subscription";

interface AppSectionProps {
  app: {
    title: string;
    subtitle: string;
    upload: {
      title: string;
      description: string;
      dragText: string;
      supportText: string;
    };
    styles: {
      title: string;
      options: Array<{
        id: string;
        name: string;
        description: string;
        image: string;
      }>;
    };
    scenes: {
      title: string;
      options: Array<{
        id: string;
        name: string;
        description: string;
        image: string;
      }>;
    };
    controls: {
      title: string;
      lighting: {
        label: string;
        options: Array<{ value: string; label: string }>;
      };
      pose: {
        label: string;
        options: Array<{ value: string; label: string }>;
      };
      quality: {
        label: string;
        options: Array<{ value: string; label: string }>;
      };
      generateBtn: string;
    };
    loading: {
      title: string;
      steps: string[];
    };
    results: {
      title: string;
      generateMore: string;
      downloadAll: string;
    };
  };
}

const styleIcons = {
  classic: Heart,
  modern: Star,
  vintage: Crown,
  bohemian: Flower,
  princess: Crown,
  minimalist: Star
};

const sceneIcons = {
  church: Church,
  beach: Waves,
  garden: TreePine,
  castle: Castle,
  vineyard: Wine,
  ballroom: Building
};

export default function AppSection({ app }: AppSectionProps) {
  const { data: session } = useSession();

  // Translation hooks
  const t = useTranslations('app.ui');

  // 主要步骤状态管理 - Main step state management
  const [currentStep, setCurrentStep] = useState<'upload' | 'style' | 'scene' | 'controls' | 'loading' | 'results'>('upload');

  // 图片上传和预览状态 - Image upload and preview state
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number>(0);
  const [isDragging, setIsDragging] = useState(false);

  // 选择状态管理 - Selection state management
  const [selectedStyle, setSelectedStyle] = useState<string>('');
  const [selectedScene, setSelectedScene] = useState<string>('');
  const [selectedLighting, setSelectedLighting] = useState<string>('natural');
  const [selectedPose, setSelectedPose] = useState<string>('portrait');
  const [selectedQuality, setSelectedQuality] = useState<string>('high');

  // 渐进式展示状态 - Progressive display state
  const [showStyleSection, setShowStyleSection] = useState(false);
  const [showSceneSection, setShowSceneSection] = useState(false);
  const [showControlsSection, setShowControlsSection] = useState(false);
  const [sectionAnimations, setSectionAnimations] = useState({
    style: false,
    scene: false,
    controls: false
  });

  // Refs for automatic scrolling - 自动滚动的引用
  const styleSectionRef = useRef<HTMLDivElement>(null);
  const sceneSectionRef = useRef<HTMLDivElement>(null);
  const controlsSectionRef = useRef<HTMLDivElement>(null);
  const resultsSectionRef = useRef<HTMLDivElement>(null);

  // 生成和加载状态 - Generation and loading state
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [loadingStep, setLoadingStep] = useState(0);
  const [generatedImages, setGeneratedImages] = useState<string[]>([]);

  // 订阅系统状态管理 - Subscription system state management
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [hasCredits, setHasCredits] = useState(true);
  const [isCheckingCredits, setIsCheckingCredits] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // 平滑滚动到指定元素的函数 - Smooth scroll to target element function
  const scrollToElement = useCallback((elementRef: React.RefObject<HTMLDivElement>, offset: number = 100) => {
    if (elementRef.current) {
      const elementTop = elementRef.current.offsetTop - offset;
      window.scrollTo({
        top: elementTop,
        behavior: 'smooth'
      });
    }
  }, []);

  // 模拟用户ID（在实际应用中应该从认证系统获取）- Mock user ID (should come from auth system in real app)
  const userId = session?.user?.email || "user_demo_123";

  // 增强的文件上传处理函数 - Enhanced file upload handler
  const handleFileUpload = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const imageFiles = fileArray.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length === 0) return;

    // 限制最多上传3张图片 - Limit to maximum 3 images
    const filesToProcess = imageFiles.slice(0, 3);

    Promise.all(
      filesToProcess.map(file => {
        return new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onload = (e) => resolve(e.target?.result as string);
          reader.readAsDataURL(file);
        });
      })
    ).then(imageUrls => {
      setUploadedImages(imageUrls);
      setSelectedImageIndex(0);

      // 启动渐进式展示 - Start progressive display
      setTimeout(() => {
        setShowStyleSection(true);
        setSectionAnimations(prev => ({ ...prev, style: true }));

        // 自动滚动到风格选择区域 - Auto scroll to style selection area
        setTimeout(() => {
          scrollToElement(styleSectionRef, 120);
        }, 200);
      }, 500);
    });
  }, [scrollToElement]);

  // 拖拽处理函数 - Drag and drop handlers
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files);
    }
  }, [handleFileUpload]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileUpload(files);
    }
  }, [handleFileUpload]);

  // 检查用户积分函数 - Check user credits function
  const checkUserCredits = useCallback(async () => {
    if (!userId) return false;

    setIsCheckingCredits(true);
    try {
      const credits = await hasEnoughCredits(userId, 1);
      setHasCredits(credits);
      return credits;
    } catch (error) {
      console.error('Error checking credits:', error);
      setHasCredits(false);
      return false;
    } finally {
      setIsCheckingCredits(false);
    }
  }, [userId]);

  // 页面加载时检查积分 - Check credits on page load
  useEffect(() => {
    if (userId) {
      checkUserCredits();
    }
  }, [userId, checkUserCredits]);

  // 渐进式风格选择处理函数 - Progressive style selection handler
  const handleStyleSelect = (styleId: string) => {
    setSelectedStyle(styleId);

    // 平滑展示场景选择区域 - Smoothly show scene selection area
    setTimeout(() => {
      setShowSceneSection(true);
      setSectionAnimations(prev => ({ ...prev, scene: true }));

      // 自动滚动到场景选择区域 - Auto scroll to scene selection area
      setTimeout(() => {
        scrollToElement(sceneSectionRef, 120);
      }, 200);
    }, 300);
  };

  // 渐进式场景选择处理函数 - Progressive scene selection handler
  const handleSceneSelect = (sceneId: string) => {
    setSelectedScene(sceneId);

    // 平滑展示控制选项区域 - Smoothly show controls section
    setTimeout(() => {
      setShowControlsSection(true);
      setSectionAnimations(prev => ({ ...prev, controls: true }));

      // 自动滚动到控制选项区域 - Auto scroll to controls section
      setTimeout(() => {
        scrollToElement(controlsSectionRef, 120);
      }, 200);
    }, 300);
  };

  const handleGenerate = async () => {
    // 检查用户登录状态 - Check user login status
    if (!session) {
      signIn();
      return;
    }

    // 检查用户积分 - Check user credits
    const hasValidCredits = await checkUserCredits();
    if (!hasValidCredits) {
      setShowSubscriptionModal(true);
      return;
    }

    setIsGenerating(true);
    setCurrentStep('loading');
    setProgress(0);
    setLoadingStep(0);

    // Simulate generation process
    const steps = app.loading.steps;
    for (let i = 0; i < steps.length; i++) {
      setLoadingStep(i);
      setProgress((i + 1) * (100 / steps.length));
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // Simulate generated results
    const mockImages = [
      "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&h=600&fit=crop",
      "https://images.unsplash.com/photo-1515934751635-c81c6bc9a2d8?w=400&h=600&fit=crop",
      "https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=400&h=600&fit=crop"
    ];

    // 消费用户积分 - Consume user credits
    if (userId && mockImages.length > 0) {
      try {
        await consumeCredits(
          userId,
          1, // 消费1个积分 - Consume 1 credit
          `Generated ${mockImages.length} AI wedding photos`,
          `Generated ${mockImages.length} AI wedding photos`,
          {
            styleCount: mockImages.length,
            generationId: `app-section-${Date.now()}`,
            selectedStyle,
            selectedScene,
            selectedLighting,
            selectedPose,
            selectedQuality
          }
        );
        console.log('💰 Successfully consumed 1 credit for AI generation');

        // 重新检查积分状态 - Recheck credit status
        await checkUserCredits();
      } catch (error) {
        console.error('❌ Failed to consume credits:', error);
      }
    }

    setGeneratedImages(mockImages);
    setIsGenerating(false);
    setCurrentStep('results');

    // 自动滚动到结果展示区域 - Auto scroll to results section
    setTimeout(() => {
      scrollToElement(resultsSectionRef, 120);
    }, 300);
  };

  // 重置应用状态函数 - Reset app state function
  const resetApp = () => {
    setCurrentStep('upload');
    setUploadedImages([]);
    setSelectedImageIndex(0);
    setSelectedStyle('');
    setSelectedScene('');
    setSelectedLighting('natural');
    setSelectedPose('portrait');
    setSelectedQuality('high');
    setGeneratedImages([]);
    setProgress(0);
    setLoadingStep(0);

    // 重置渐进式展示状态 - Reset progressive display state
    setShowStyleSection(false);
    setShowSceneSection(false);
    setShowControlsSection(false);
    setSectionAnimations({
      style: false,
      scene: false,
      controls: false
    });

    // 滚动到页面顶部 - Scroll to top of page
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <section id="app" className="relative flex items-center justify-center min-h-[calc(100vh-4rem)] w-full py-12 md:py-24 lg:py-32 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16 relative">
          {/* 顶部积分显示 - Top credit display */}
          {session && (
            <div className="absolute top-0 right-0 hidden md:block">
              <CreditDisplay
                userId={userId}
                onUpgradeClick={() => setShowSubscriptionModal(true)}
                showUpgradeButton={true}
              />
            </div>
          )}

          <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent mb-4">
            {app.title}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {app.subtitle}
          </p>

          {/* 移动端积分显示 - Mobile credit display */}
          {session && (
            <div className="mt-6 md:hidden flex justify-center">
              <CreditDisplay
                userId={userId}
                onUpgradeClick={() => setShowSubscriptionModal(true)}
                showUpgradeButton={true}
              />
            </div>
          )}
        </div>

        {/* App Container */}
        <div className="max-w-6xl mx-auto">
          {/* Upload Section */}
          <Card className="border-2 border-dashed border-pink-300 bg-white/50 backdrop-blur-sm mb-8">
            <CardHeader>
              <CardTitle className="text-center text-2xl font-bold text-gray-800">
                {app.upload.title}
              </CardTitle>
              <p className="text-center text-gray-600">
                {app.upload.description}
              </p>
            </CardHeader>
            <CardContent>
              {uploadedImages.length === 0 ? (
                // 上传区域 - Upload area
                <div
                  className={`border-2 border-dashed rounded-lg p-12 text-center transition-all duration-300 cursor-pointer ${
                    isDragging
                      ? "border-pink-500 bg-pink-50 scale-105"
                      : "border-pink-300 hover:border-pink-400 hover:bg-pink-25"
                  }`}
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onClick={() => fileInputRef.current?.click()}
                >
                  <div className={`transition-transform duration-300 ${isDragging ? 'scale-110' : ''}`}>
                    <Upload className="w-16 h-16 text-pink-500 mx-auto mb-4" />
                    <p className="text-lg font-medium text-gray-700 mb-2">
                      {isDragging ? t('upload.dragDropHint') : app.upload.dragText}
                    </p>
                    <p className="text-sm text-gray-500 mb-4">
                      {app.upload.supportText}
                    </p>
                    <p className="text-xs text-gray-400">
                      {t('upload.multipleUploadTip')}
                    </p>
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </div>
              ) : (
                // 图片预览区域 - Image preview area
                <div className="space-y-6">
                  {/* 主图片预览 - Main image preview */}
                  <div className="relative">
                    <div className="aspect-[4/3] relative rounded-lg overflow-hidden shadow-lg">
                      <Image
                        src={uploadedImages[selectedImageIndex]}
                        alt={`${t('upload.uploadedImageAlt')} ${selectedImageIndex + 1}`}
                        fill
                        className="object-cover"
                      />
                      <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-medium text-gray-700">
                        {selectedImageIndex + 1} / {uploadedImages.length}
                      </div>
                    </div>
                  </div>

                  {/* 缩略图网格 - Thumbnail grid */}
                  {uploadedImages.length > 1 && (
                    <div className="flex justify-center space-x-4">
                      {uploadedImages.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => setSelectedImageIndex(index)}
                          className={`relative w-20 h-20 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                            selectedImageIndex === index
                              ? 'border-pink-500 ring-2 ring-pink-200'
                              : 'border-gray-200 hover:border-pink-300'
                          }`}
                        >
                          <Image
                            src={image}
                            alt={`${t('upload.thumbnailAlt')} ${index + 1}`}
                            fill
                            className="object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  )}

                  {/* 操作按钮 - Action buttons */}
                  <div className="flex justify-center space-x-4">
                    <Button
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      className="border-pink-300 text-pink-600 hover:bg-pink-50"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      {t('upload.addMoreImages')}
                    </Button>
                    <Button
                      variant="ghost"
                      onClick={resetApp}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      <X className="w-4 h-4 mr-2" />
                      {t('upload.reselect')}
                    </Button>
                  </div>

                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* 渐进式风格选择区域 - Progressive Style Selection */}
          {showStyleSection && (
            <Card
              ref={styleSectionRef}
              className={`bg-white/50 backdrop-blur-sm mb-8 transition-all duration-700 transform ${
                sectionAnimations.style ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
              }`}>
              <CardHeader>
                <div className="flex items-center justify-center mb-4">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white font-bold text-sm">1</span>
                    </div>
                    <CardTitle className="text-2xl font-bold text-gray-800">
                      {app.styles.title}
                    </CardTitle>
                  </div>
                </div>
                <p className="text-center text-gray-600">{t('styleSelection.subtitle')}</p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                  {app.styles.options.map((style) => {
                    const IconComponent = styleIcons[style.id as keyof typeof styleIcons] || Heart;
                    const isSelected = selectedStyle === style.id;
                    return (
                      <div
                        key={style.id}
                        className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                          isSelected
                            ? 'border-pink-500 ring-2 ring-pink-200 shadow-lg scale-105'
                            : 'border-transparent hover:border-pink-400 hover:shadow-md'
                        }`}
                        onClick={() => handleStyleSelect(style.id)}
                      >
                        <div className="aspect-[3/4] relative">
                          <Image
                            src={style.image}
                            alt={style.name}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

                          {/* 选中状态指示器 - Selected state indicator */}
                          {isSelected && (
                            <div className="absolute top-4 right-4 bg-pink-500 text-white rounded-full p-2">
                              <Check className="w-4 h-4" />
                            </div>
                          )}

                          <div className="absolute bottom-4 left-4 right-4 text-white">
                            <div className="flex items-center mb-2">
                              <IconComponent className="w-5 h-5 mr-2" />
                              <h3 className="font-semibold">{style.name}</h3>
                            </div>
                            <p className="text-sm opacity-90">{style.description}</p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* 选择提示 - Selection hint */}
                {selectedStyle && (
                  <div className="mt-6 text-center">
                    <div className="inline-flex items-center px-4 py-2 bg-green-50 text-green-700 rounded-full text-sm border border-green-200">
                      <Check className="w-4 h-4 mr-2" />
                      {t('styleSelection.selectionHint')}
                      <ChevronDown className="w-4 h-4 ml-2 animate-bounce" />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* 渐进式场景选择区域 - Progressive Scene Selection */}
          {showSceneSection && (
            <Card
              ref={sceneSectionRef}
              className={`bg-white/50 backdrop-blur-sm mb-8 transition-all duration-700 transform ${
                sectionAnimations.scene ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
              }`}>
              <CardHeader>
                <div className="flex items-center justify-center mb-4">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white font-bold text-sm">2</span>
                    </div>
                    <CardTitle className="text-2xl font-bold text-gray-800">
                      {app.scenes.title}
                    </CardTitle>
                  </div>
                </div>
                <p className="text-center text-gray-600">{t('sceneSelection.subtitle')}</p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                  {app.scenes.options.map((scene) => {
                    const IconComponent = sceneIcons[scene.id as keyof typeof sceneIcons] || Mountain;
                    const isSelected = selectedScene === scene.id;
                    return (
                      <div
                        key={scene.id}
                        className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                          isSelected
                            ? 'border-pink-500 ring-2 ring-pink-200 shadow-lg scale-105'
                            : 'border-transparent hover:border-pink-400 hover:shadow-md'
                        }`}
                        onClick={() => handleSceneSelect(scene.id)}
                      >
                        <div className="aspect-[4/3] relative">
                          <Image
                            src={scene.image}
                            alt={scene.name}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

                          {/* 选中状态指示器 - Selected state indicator */}
                          {isSelected && (
                            <div className="absolute top-4 right-4 bg-pink-500 text-white rounded-full p-2">
                              <Check className="w-4 h-4" />
                            </div>
                          )}

                          <div className="absolute bottom-4 left-4 right-4 text-white">
                            <div className="flex items-center mb-2">
                              <IconComponent className="w-5 h-5 mr-2" />
                              <h3 className="font-semibold">{scene.name}</h3>
                            </div>
                            <p className="text-sm opacity-90">{scene.description}</p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* 选择提示 - Selection hint */}
                {selectedScene && (
                  <div className="mt-6 text-center">
                    <div className="inline-flex items-center px-4 py-2 bg-green-50 text-green-700 rounded-full text-sm border border-green-200">
                      <Check className="w-4 h-4 mr-2" />
                      {t('sceneSelection.selectionHint')}
                      <ChevronDown className="w-4 h-4 ml-2 animate-bounce" />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* 渐进式控制选项区域 - Progressive Controls Section */}
          {showControlsSection && (
            <Card
              ref={controlsSectionRef}
              className={`bg-white/50 backdrop-blur-sm mb-8 transition-all duration-700 transform ${
                sectionAnimations.controls ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
              }`}>
              <CardHeader>
                <div className="flex items-center justify-center mb-4">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white font-bold text-sm">3</span>
                    </div>
                    <CardTitle className="text-2xl font-bold text-gray-800">
                      {app.controls.title}
                    </CardTitle>
                  </div>
                </div>
                <p className="text-center text-gray-600">{t('controls.subtitle')}</p>
              </CardHeader>
              <CardContent className="space-y-8">
                {/* 参数选择网格 - Parameter selection grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* 光线控制 - Lighting Control */}
                  <div className="space-y-3">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                      <Sparkles className="w-4 h-4 mr-2 text-pink-500" />
                      {app.controls.lighting.label}
                    </label>
                    <select
                      value={selectedLighting}
                      onChange={(e) => setSelectedLighting(e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-200 hover:border-pink-300"
                    >
                      {app.controls.lighting.options.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* 姿势控制 - Pose Control */}
                  <div className="space-y-3">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                      <Camera className="w-4 h-4 mr-2 text-pink-500" />
                      {app.controls.pose.label}
                    </label>
                    <select
                      value={selectedPose}
                      onChange={(e) => setSelectedPose(e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-200 hover:border-pink-300"
                    >
                      {app.controls.pose.options.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* 质量控制 - Quality Control */}
                  <div className="space-y-3">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                      <Settings className="w-4 h-4 mr-2 text-pink-500" />
                      {app.controls.quality.label}
                    </label>
                    <select
                      value={selectedQuality}
                      onChange={(e) => setSelectedQuality(e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-200 hover:border-pink-300"
                    >
                      {app.controls.quality.options.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* 选择总结 - Selection summary */}
                <div className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-lg p-6 border border-pink-200">
                  <h4 className="font-semibold text-gray-800 mb-4 flex items-center">
                    <Eye className="w-5 h-5 mr-2 text-pink-500" />
                    {t('controls.preview.title')}
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">{t('controls.preview.selectedStyle')}</span>
                        <span className="font-medium text-gray-800">
                          {app.styles.options.find(s => s.id === selectedStyle)?.name || t('controls.preview.notSelected')}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">{t('controls.preview.selectedScene')}</span>
                        <span className="font-medium text-gray-800">
                          {app.scenes.options.find(s => s.id === selectedScene)?.name || t('controls.preview.notSelected')}
                        </span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">{t('controls.preview.lightingEffect')}</span>
                        <span className="font-medium text-gray-800">
                          {app.controls.lighting.options.find(o => o.value === selectedLighting)?.label}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">{t('controls.preview.imageCount')}</span>
                        <span className="font-medium text-gray-800">{uploadedImages.length} {t('controls.preview.unit')}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 积分显示和生成按钮区域 - Credit display and generate button section */}
                <div className="space-y-8 pt-6">
                  {/* 积分显示 - Credit display */}
                  <div className="flex justify-center">
                    <CreditDisplay
                      userId={userId}
                      onUpgradeClick={() => setShowSubscriptionModal(true)}
                      showUpgradeButton={true}
                      className="mb-4"
                    />
                  </div>

                  {/* 生成准备状态检查 - Generation readiness check */}
                  <div className="max-w-md mx-auto">
                    <div className="space-y-3">
                      {/* 进度指示器 - Progress indicators */}
                      <div className="flex items-center justify-between text-sm">
                        <div className={`flex items-center ${uploadedImages.length > 0 ? 'text-green-600' : 'text-gray-400'}`}>
                          <div className={`w-4 h-4 rounded-full mr-2 ${uploadedImages.length > 0 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                          {t('controls.readiness.uploadImages')} ({uploadedImages.length}/3)
                        </div>
                        {uploadedImages.length > 0 && <Check className="w-4 h-4 text-green-500" />}
                      </div>

                      <div className="flex items-center justify-between text-sm">
                        <div className={`flex items-center ${selectedStyle ? 'text-green-600' : 'text-gray-400'}`}>
                          <div className={`w-4 h-4 rounded-full mr-2 ${selectedStyle ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                          {t('controls.readiness.selectStyle')}
                        </div>
                        {selectedStyle && <Check className="w-4 h-4 text-green-500" />}
                      </div>

                      <div className="flex items-center justify-between text-sm">
                        <div className={`flex items-center ${selectedScene ? 'text-green-600' : 'text-gray-400'}`}>
                          <div className={`w-4 h-4 rounded-full mr-2 ${selectedScene ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                          {t('controls.readiness.selectScene')}
                        </div>
                        {selectedScene && <Check className="w-4 h-4 text-green-500" />}
                      </div>
                    </div>
                  </div>

                  {/* 积分状态提示 - Credit status notice */}
                  {!hasCredits && (
                    <div className="max-w-md mx-auto p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="flex items-start">
                        <AlertCircle className="h-5 w-5 text-yellow-500 mr-3 mt-0.5 flex-shrink-0" />
                        <div>
                          <p className="text-yellow-800 font-medium text-sm">
                            {t('controls.credits.insufficient')}
                          </p>
                          <p className="text-yellow-700 text-xs mt-1">
                            {t('controls.credits.insufficientDescription')}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 生成按钮 - Generate button */}
                  <div className="text-center">
                    {/* 主生成按钮 - Main generate button */}
                    <Button
                      onClick={handleGenerate}
                      size="lg"
                      disabled={!hasCredits || isGenerating || isCheckingCredits || !selectedStyle || !selectedScene || uploadedImages.length === 0}
                      className={`px-16 py-6 rounded-full text-xl font-bold shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:scale-110 disabled:transform-none disabled:opacity-50 ${
                        hasCredits && !isGenerating && !isCheckingCredits && selectedStyle && selectedScene && uploadedImages.length > 0
                          ? 'bg-gradient-to-r from-pink-500 via-purple-500 to-pink-600 hover:from-pink-600 hover:via-purple-600 hover:to-pink-700 text-white animate-pulse'
                          : !hasCredits
                          ? 'bg-gradient-to-r from-gray-400 to-gray-500 text-white cursor-not-allowed'
                          : 'bg-gradient-to-r from-gray-300 to-gray-400 text-gray-600 cursor-not-allowed'
                      }`}
                    >
                      {isCheckingCredits ? (
                        <>
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white mr-4"></div>
                          {t('controls.credits.checking')}
                        </>
                      ) : isGenerating ? (
                        <>
                          <Sparkles className="w-7 h-7 mr-4 animate-spin" />
                          {t('controls.generation.creatingMagic')}
                        </>
                      ) : !hasCredits ? (
                        <>
                          <Lock className="w-7 h-7 mr-4" />
                          {t('controls.credits.subscriptionRequired')}
                        </>
                      ) : !selectedStyle || !selectedScene || uploadedImages.length === 0 ? (
                        <>
                          <Settings className="w-7 h-7 mr-4" />
                          {t('controls.credits.completeSettings')}
                        </>
                      ) : (
                        <>
                          <Sparkles className="w-7 h-7 mr-4" />
                          <span className="bg-gradient-to-r from-yellow-200 to-yellow-100 bg-clip-text text-transparent">
                            ✨ {app.controls.generateBtn} ✨
                          </span>
                        </>
                      )}
                    </Button>

                    {/* 生成提示信息 - Generation hint */}
                    {selectedStyle && selectedScene && uploadedImages.length > 0 && hasCredits && (
                      <div className="mt-4 text-sm text-gray-600">
                        <p className="flex items-center justify-center">
                          <Zap className="w-4 h-4 mr-2 text-yellow-500" />
                          {t('controls.generation.estimatedTime')}
                        </p>
                      </div>
                    )}

                    {/* 升级提示按钮 - Upgrade prompt button */}
                    {!hasCredits && (
                      <div className="mt-6">
                        <Button
                          onClick={() => setShowSubscriptionModal(true)}
                          variant="outline"
                          size="lg"
                          className="border-purple-300 text-purple-600 hover:bg-purple-50 px-8 py-3"
                        >
                          <Crown className="w-5 h-5 mr-3" />
                          {t('controls.credits.viewPlans')}
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 增强的加载状态区域 - Enhanced Loading Section */}
          {currentStep === 'loading' && (
            <Card className="bg-white/50 backdrop-blur-sm">
              <CardContent className="py-16">
                <div className="text-center space-y-8">
                  {/* 动画加载指示器 - Animated loading indicator */}
                  <div className="relative w-32 h-32 mx-auto">
                    <div className="absolute inset-0 border-4 border-pink-200 rounded-full"></div>
                    <div className="absolute inset-0 border-4 border-pink-500 rounded-full border-t-transparent animate-spin"></div>
                    <div className="absolute inset-0 border-4 border-purple-300 rounded-full border-b-transparent animate-spin animate-reverse" style={{ animationDuration: '3s' }}></div>
                    <Sparkles className="absolute inset-0 w-10 h-10 m-auto text-pink-500 animate-pulse" />
                  </div>

                  {/* 加载标题和描述 - Loading title and description */}
                  <div className="space-y-4">
                    <h3 className="text-3xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                      {app.loading.title}
                    </h3>
                    <p className="text-xl text-gray-600 max-w-md mx-auto">
                      {app.loading.steps[loadingStep]}
                    </p>
                  </div>

                  {/* 进度条和百分比 - Progress bar and percentage */}
                  <div className="max-w-lg mx-auto space-y-4">
                    <div className="relative">
                      <div className="w-full bg-gray-200 rounded-full h-4 shadow-inner">
                        <div
                          className="bg-gradient-to-r from-pink-500 via-purple-500 to-pink-600 h-4 rounded-full transition-all duration-500 shadow-lg"
                          style={{ width: `${progress}%` }}
                        >
                          <div className="h-full bg-white/30 rounded-full animate-pulse"></div>
                        </div>
                      </div>
                      <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                        <span className="text-xs font-bold text-white drop-shadow-lg">
                          {Math.round(progress)}%
                        </span>
                      </div>
                    </div>

                    {/* 步骤指示器 - Step indicators */}
                    <div className="flex justify-center space-x-2">
                      {app.loading.steps.map((_, index) => (
                        <div
                          key={index}
                          className={`w-3 h-3 rounded-full transition-all duration-300 ${
                            index <= loadingStep
                              ? 'bg-gradient-to-r from-pink-500 to-purple-600 scale-110'
                              : 'bg-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                  </div>

                  {/* 生成信息展示 - Generation info display */}
                  <div className="max-w-md mx-auto bg-gradient-to-r from-pink-50 to-purple-50 rounded-lg p-6 border border-pink-200">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="text-center">
                        <div className="font-semibold text-gray-800">{t('loading.processingImages')}</div>
                        <div className="text-pink-600">{uploadedImages.length} {t('controls.preview.unit')}</div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold text-gray-800">{t('loading.generationStyle')}</div>
                        <div className="text-purple-600">
                          {app.styles.options.find(s => s.id === selectedStyle)?.name}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 温馨提示 - Friendly tips */}
                  <div className="text-sm text-gray-500 max-w-md mx-auto">
                    <p className="flex items-center justify-center">
                      <Heart className="w-4 h-4 mr-2 text-pink-400" />
                      {t('loading.friendlyTip')}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 增强的结果展示区域 - Enhanced Results Section */}
          {currentStep === 'results' && (
            <Card
              ref={resultsSectionRef}
              className="bg-white/50 backdrop-blur-sm">
              <CardHeader>
                <div className="text-center space-y-4">
                  <div className="flex items-center justify-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center mr-4">
                      <Sparkles className="w-6 h-6 text-white" />
                    </div>
                    <CardTitle className="text-3xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                      {app.results.title}
                    </CardTitle>
                  </div>
                  <p className="text-gray-600 max-w-2xl mx-auto">
                    {t('results.description')}
                  </p>
                </div>
              </CardHeader>
              <CardContent className="space-y-10">
                {/* 生成统计信息 - Generation statistics */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg border border-pink-200">
                    <div className="text-2xl font-bold text-pink-600">{generatedImages.length}</div>
                    <div className="text-sm text-gray-600">{t('results.statistics.generatedImages')}</div>
                  </div>
                  <div className="text-center p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200">
                    <div className="text-2xl font-bold text-purple-600">
                      {app.styles.options.find(s => s.id === selectedStyle)?.name}
                    </div>
                    <div className="text-sm text-gray-600">{t('results.statistics.selectedStyle')}</div>
                  </div>
                  <div className="text-center p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                    <div className="text-2xl font-bold text-blue-600">4K</div>
                    <div className="text-sm text-gray-600">{t('results.statistics.hdQuality')}</div>
                  </div>
                </div>

                {/* 增强的图片网格 - Enhanced image grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {generatedImages.map((image, index) => (
                    <div key={index} className="relative group">
                      {/* 图片容器 - Image container */}
                      <div className="relative aspect-[3/4] rounded-xl overflow-hidden shadow-xl border-4 border-white group-hover:shadow-2xl transition-all duration-500 transform group-hover:scale-105">
                        <Image
                          src={image}
                          alt={`${t('results.imageActions.aiWeddingPhoto')} ${index + 1}`}
                          fill
                          className="object-cover transition-transform duration-700 group-hover:scale-110"
                        />

                        {/* 悬停遮罩 - Hover overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                        {/* 操作按钮 - Action buttons */}
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <div className="flex space-x-3">
                            <Button
                              size="sm"
                              className="bg-white/90 text-gray-800 hover:bg-white shadow-lg"
                              onClick={() => {
                                // 预览功能 - Preview functionality
                                window.open(image, '_blank');
                              }}
                            >
                              <Eye className="w-4 h-4 mr-2" />
                              {t('results.imageActions.preview')}
                            </Button>
                            <Button
                              size="sm"
                              className="bg-pink-500 text-white hover:bg-pink-600 shadow-lg"
                              onClick={() => {
                                // 下载功能 - Download functionality
                                const link = document.createElement('a');
                                link.href = image;
                                link.download = `ai-wedding-photo-${index + 1}.jpg`;
                                link.click();
                              }}
                            >
                              <Download className="w-4 h-4 mr-2" />
                              {t('results.imageActions.download')}
                            </Button>
                          </div>
                        </div>

                        {/* 图片标签 - Image label */}
                        <div className="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <div className="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2">
                            <p className="text-sm font-medium text-gray-800">
                              {t('results.imageActions.aiWeddingPhoto')} #{index + 1}
                            </p>
                            <p className="text-xs text-gray-600">
                              {app.styles.options.find(s => s.id === selectedStyle)?.name} {t('results.imageActions.style')}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* 操作按钮区域 - Action buttons area */}
                <div className="space-y-6">
                  {/* 主要操作按钮 - Primary action buttons */}
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button
                      onClick={() => {
                        // 批量下载所有图片 - Batch download all images
                        generatedImages.forEach((image, index) => {
                          setTimeout(() => {
                            const link = document.createElement('a');
                            link.href = image;
                            link.download = `ai-wedding-photo-${index + 1}.jpg`;
                            link.click();
                          }, index * 500);
                        });
                      }}
                      size="lg"
                      className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white px-8 py-4 shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      <Download className="w-5 h-5 mr-3" />
                      {app.results.downloadAll}
                    </Button>

                    <Button
                      onClick={() => {
                        // 重置到控制面板继续生成 - Reset to controls panel for more generation
                        setShowControlsSection(true);
                        setSectionAnimations(prev => ({ ...prev, controls: true }));

                        // 自动滚动到控制选项区域 - Auto scroll to controls section
                        setTimeout(() => {
                          scrollToElement(controlsSectionRef, 120);
                        }, 200);
                      }}
                      variant="outline"
                      size="lg"
                      className="border-pink-300 text-pink-600 hover:bg-pink-50 px-8 py-4"
                    >
                      <Plus className="w-5 h-5 mr-3" />
                      {app.results.generateMore}
                    </Button>
                  </div>

                  {/* 次要操作按钮 - Secondary action buttons */}
                  <div className="text-center space-y-4">
                    <Button
                      onClick={resetApp}
                      variant="ghost"
                      className="text-gray-500 hover:text-gray-700"
                    >
                      <Camera className="w-4 h-4 mr-2" />
                      {t('results.actions.startOver')}
                    </Button>

                    {/* 存储提示 - Storage notice */}
                    <p className="text-xs text-gray-500 max-w-md mx-auto">
                      {t('results.actions.storageNotice')}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* 订阅模态框 - Subscription modal */}
      <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={() => setShowSubscriptionModal(false)}
        userId={userId}
        onSubscriptionChange={async () => {
          // 重新检查积分状态 - Recheck credit status
          await checkUserCredits();
        }}
        requiredCredits={1}
      />
    </section>
  );
}
